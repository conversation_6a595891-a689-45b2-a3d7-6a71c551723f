package models

import (
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"time"
)

type Invoice struct {
	Id                   uint             `json:"id"`
	ClientId             uint             `json:"client_id"`
	CreatedById          *uint            `json:"created_by_id"`
	CouponId             *uint            `json:"coupon_id"`
	TaxRateId            *uint            `json:"tax_rate_id"`
	PaymentTypeId        *uint            `json:"payment_type_id"`
	CurrencyId           *uint            `json:"currency_id"`
	Reference            *string          `json:"reference"`
	Date                 *time.Time       `json:"date"`
	DueDate              *time.Time       `json:"due_date"`
	Amount               *float64         `json:"amount"`
	Discount             *float64         `json:"discount"`
	DiscountType         *string          `json:"discount_type" gorm:"default:'percentage'"`
	Balance              *float64         `json:"balance"`
	Status               string           `json:"status" gorm:"default:'draft'"`
	PaymentStatus        *string          `json:"payment_status" gorm:"default:'unpaid'"`
	Xrate                *float64         `json:"xrate" gorm:"default:1"`
	DiscountAmount       *float64         `json:"discount_amount"`
	CouponDiscountAmount *float64         `json:"coupon_discount_amount"`
	TaxAmount            *float64         `json:"tax_amount"`
	Viewed               *bool            `json:"viewed" gorm:"default:false"`
	ViewedAt             *time.Time       `json:"viewed_at"`
	AdminNotes           *string          `json:"admin_notes" gorm:"type:text"`
	Terms                *string          `json:"terms" gorm:"type:text"`
	Sent                 *bool            `json:"sent" gorm:"default:false"`
	SentAt               *time.Time       `json:"sent_at"`
	PaymentData          *string          `json:"payment_data" gorm:"type:text"`
	Recurring            *bool            `json:"recurring" gorm:"default:false"`
	RecurFrequency       *uint            `json:"recur_frequency"`
	RecurStartDate       *time.Time       `json:"recur_start_date"`
	RecurEndDate         *time.Time       `json:"recur_end_date"`
	RecurNextDate        *time.Time       `json:"recur_next_date"`
	Description          *string          `json:"description" gorm:"type:text"`
	CreatedAt            *time.Time       `json:"created_at"`
	UpdatedAt            *time.Time       `json:"updated_at"`
	InvoiceItems         []InvoiceItem    `json:"items"`
	PaymentType          PaymentType      `json:"payment_type"`
	Client               Client           `json:"client"`
	Currency             Currency         `json:"currency"`
	InvoicePayments      []InvoicePayment `json:"payments"`
	IsSubscription       *bool            `json:"is_subscription" gorm:"default:false"`
}
type InvoiceRequest struct {
	ClientId             uint       `json:"client_id" binding:"required"`
	CouponId             *uint      `json:"coupon_id,omitempty"`
	TaxRateId            *uint      `json:"tax_rate_id,omitempty"`
	PaymentTypeId        *uint      `json:"payment_type_id,omitempty"`
	CurrencyId           *uint      `json:"currency_id" binding:"required"`
	IsSubscription       *bool      `json:"is_subscription,omitempty"`
	Reference            *string    `json:"reference,omitempty"`
	Date                 *time.Time `json:"date" binding:"required"`
	DueDate              *time.Time `json:"due_date,omitempty"`
	Amount               *float64   `json:"amount" binding:"required"`
	Discount             *float64   `json:"discount,omitempty"`
	DiscountType         *string    `json:"discount_type,omitempty"`
	Status               string     `json:"status,omitempty"`
	Balance              *float64   `json:"balance"`
	Xrate                *float64   `json:"xrate"`
	DiscountAmount       *float64   `json:"discount_amount,omitempty"`
	CouponDiscountAmount *float64   `json:"coupon_discount_amount,omitempty"`
	TaxAmount            *float64   `json:"tax_amount"`
	Viewed               *bool      `json:"viewed,omitempty"`
	ViewedAt             *time.Time `json:"viewed_at,omitempty"`
	AdminNotes           *string    `json:"admin_notes,omitempty"`
	Terms                *string    `json:"terms,omitempty"`
	PaymentData          *string    `json:"payment_data,omitempty"`
	Recurring            *bool      `json:"recurring,omitempty"`
	RecurFrequency       *uint      `json:"recur_frequency,omitempty"`
	RecurStartDate       *time.Time `json:"recur_start_date,omitempty"`
	RecurEndDate         *time.Time `json:"recur_end_date,omitempty"`
	RecurNextDate        *time.Time `json:"recur_next_date,omitempty"`
	Description          *string    `json:"description,omitempty"`
	InvoiceItems         []InvoiceItemRequest
}

// InvoiceView is used for rendering invoice data in templates and PDF generation.
type InvoiceView struct {
	Reference    string
	Date         string
	DueDate      string
	Terms        string
	BillTo       PartyView
	BillFrom     PartyView
	InvoiceItems []InvoiceItemView
	Subtotal     float64
	TaxAmount    float64
	Total        float64
	Logo         string
}

type PartyView struct {
	Name    string
	Company string
	Address string
	City    string
	State   string
	Country string
	Email   string
	Phone   string
}

type InvoiceItemView struct {
	Description string
	Qty         int
	UnitPrice   float64
	Total       float64
}

// ToInvoiceView maps a models.Invoice to an InvoiceView for rendering.
func ToInvoiceView(invoice Invoice) InvoiceView {
	var items []InvoiceItemView
	var subtotal float64

	for _, item := range invoice.InvoiceItems {
		qty := derefUint(item.Quantity)
		unit := derefFloat(item.UnitCost)
		total := derefFloat(item.Total)
		if total == 0 && qty > 0 && unit > 0 {
			total = float64(qty) * unit
		}
		subtotal += total
		items = append(items, InvoiceItemView{
			Description: derefStr(item.Name),
			Qty:         qty,
			UnitPrice:   unit,
			Total:       total,
		})
	}
	fmt.Println("Here")
	company := PartyView{
		Name:    "Yotracker",
		Company: "Yotracker",
		Address: "Harare",
		City:    "Harare",
		State:   "Harare",
		Country: "Zimbabwe",
		Email:   "<EMAIL>",
		Phone:   "+263 *********",
	}
	fmt.Println("After Here")
	company.Name = GetSetting("company_name")
	company.Address = GetSetting("company_address")
	company.Phone = GetSetting("company_phone")
	company.Email = GetSetting("company_email")
	fmt.Println("Company details", company)
	return InvoiceView{
		Reference:    derefStr(invoice.Reference),
		Date:         formatDate(invoice.Date),
		DueDate:      formatDate(invoice.DueDate),
		Terms:        derefStr(invoice.Terms),
		BillTo:       mapClientToPartyView(invoice.Client),
		BillFrom:     company,
		InvoiceItems: items,
		Subtotal:     subtotal,
		TaxAmount:    derefFloat(invoice.TaxAmount),
		Total:        derefFloat(invoice.Amount),
		Logo:         "https://ignition.co.zw/website/images/logo.png",
	}
}

func mapClientToPartyView(c Client) PartyView {
	return PartyView{
		Name:    c.Name,
		Company: derefStr(c.Company),
		Address: derefStr(c.Address),
		City:    derefStr(c.City),
		State:   derefStr(c.State),
		Country: derefStr(&c.Country.Name),
		Email:   c.Email,
	}
}

func derefStr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func derefFloat(f *float64) float64 {
	if f == nil {
		return 0
	}
	return *f
}

func derefUint(u *uint) int {
	if u == nil {
		return 0
	}
	return int(*u)
}

func formatDate(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02")
}

// GetLogoAsBase64 reads the logo file and returns it as a base64 data URI
func GetLogoAsBase64() string {
	logoPath := "assets/logo.png"

	// Check if the logo exists
	if _, err := os.Stat(logoPath); os.IsNotExist(err) {
		fmt.Printf("Logo file not found at %s\n", logoPath)
		return ""
	}

	// Open and read the file
	file, err := os.Open(logoPath)
	if err != nil {
		fmt.Printf("Error opening logo file: %v\n", err)
		return ""
	}
	defer file.Close()

	// Read file content
	fileContent, err := io.ReadAll(file)
	if err != nil {
		fmt.Printf("Error reading logo file: %v\n", err)
		return ""
	}

	// Convert to base64 and create data URI
	base64Content := base64.StdEncoding.EncodeToString(fileContent)
	return fmt.Sprintf("data:image/png;base64,%s", base64Content)
}
