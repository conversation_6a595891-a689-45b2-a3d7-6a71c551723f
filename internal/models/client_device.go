package models

import (
	"encoding/json"
	"time"
)

type ClientDevice struct {
	Id                    uint             `json:"id" gorm:"primaryKey"`
	CreatedById           uint             `json:"created_by_id"`
	DeviceTypeId          uint             `json:"device_type_id" gorm:"index"`
	DeviceType            DeviceType       `json:"device_type"`
	ClientId              uint             `json:"client_id" gorm:"index"`
	Client                Client           `json:"client"`
	AssetType             string           `json:"asset_type" gorm:"default:'vehicle'"`
	DriverId              *uint            `json:"driver_id" gorm:"index"`
	FleetId               *uint            `json:"fleet_id"`
	Fleet                 *Fleet           `json:"fleet"`
	Name                  *string          `json:"name"`
	PhoneNumber           *string          `json:"phone_number"`
	DeviceId              string           `json:"device_id"`
	Password              *string          `json:"password"`
	AlertPhoneNumber      *string          `json:"alert_phone_number" gorm:"type:text"`
	PlateNumber           *string          `json:"plate_number"`
	MaxSpeed              *float64         `json:"max_speed"`
	MaxSpeedDuration      *int             `json:"max_speed_duration"`
	Amount                *float64         `json:"amount"`
	BillingCycle          *uint            `json:"billing_cycle"`
	IsLifetime            *bool            `json:"is_lifetime" gorm:"default:false"`
	NextBillingDate       *time.Time       `json:"next_billing_date" gorm:"type:date"`
	SubscriptionStartDate *time.Time       `json:"subscription_start_date" gorm:"type:date"`
	SubscriptionEndDate   *time.Time       `json:"subscription_end_date" gorm:"type:date"`
	Icon                  *string          `json:"icon"`
	Model                 *string          `json:"model"`
	Make                  *string          `json:"make"`
	ManufactureYear       *string          `json:"manufacture_year"`
	Color                 *string          `json:"color"`
	EngineNumber          *string          `json:"engine_number"`
	ChassisNumber         *string          `json:"chassis_number"`
	Vin                   *string          `json:"vin"`
	ObdProtocol           *string          `json:"obd_protocol"`
	FuelType              *string          `json:"fuel_type"`
	FuelRate              *float64         `json:"fuel_rate"`
	FuelRateUnit          *string          `json:"fuel_rate_unit"`
	Manufacturer          *string          `json:"manufacturer"`
	Status                string           `json:"status" gorm:"default:'active'"`
	DeviceStatus          *string          `json:"device_status" gorm:"default:'offline'"`
	InsuranceExpiryDate   *time.Time       `json:"insurance_expiry_date" gorm:"type:date"`
	InitialMileage        *float64         `json:"initial_mileage"`
	Mileage               *float64         `json:"mileage"`
	CurrentMileage        *float64         `json:"current_mileage"`
	ServiceMileage        *float64         `json:"service_mileage"`
	DistanceCovered       *float64         `json:"distance_covered"`
	ServiceDays           *uint            `json:"service_days"`
	LastServiceDate       *time.Time       `json:"last_service_date"`
	NextServiceDate       *time.Time       `json:"next_service_date"`
	LastServiceMileage    *float64         `json:"last_service_mileage"`
	NextServiceMileage    *float64         `json:"next_service_mileage"`
	Latitude              *float64         `json:"latitude"`
	Longitude             *float64         `json:"longitude"`
	Connected             bool             `json:"connected" gorm:"default:false"`
	Emails                *json.RawMessage `json:"emails" gorm:"type:json"`
	PhoneNumbers          *json.RawMessage `json:"phone_numbers" gorm:"type:json"`
	Description           *string          `json:"description" gorm:"type:text"`
	AlarmEvents           bool             `json:"alarm_events" gorm:"default:true"`
	TowingEvents          bool             `json:"towing_events" gorm:"default:true"`
	TripStartEvents       bool             `json:"trip_start_events" gorm:"default:true"`
	TripEndEvents         bool             `json:"trip_end_events" gorm:"default:true"`
	GeofenceEntryEvents   bool             `json:"geofence_entry_events" gorm:"default:true"`
	GeofenceExitEvents    bool             `json:"geofence_exit_events" gorm:"default:true"`
	BatteryLevel          *float64         `json:"battery_level"`
	SignalStrength        *float64         `json:"signal_strength"`
	LastStatusUpdate      *time.Time       `json:"last_status_update"`
	CreatedAt             time.Time        `json:"created_at"`
	UpdatedAt             time.Time        `json:"updated_at"`
}
type ClientDeviceRequest struct {
	DeviceTypeId          uint             `json:"device_type_id" binding:"required"`
	ClientId              uint             `json:"client_id" binding:"required"`
	AssetType             string           `json:"asset_type" binding:"required"`
	DriverId              *uint            `json:"driver_id"`
	FleetId               *uint            `json:"fleet_id"`
	Name                  *string          `json:"name"`
	PhoneNumber           *string          `json:"phone_number,omitempty"`
	DeviceId              string           `json:"device_id,omitempty"`
	Password              *string          `json:"password,omitempty"`
	AlertPhoneNumber      *string          `json:"alert_phone_number,omitempty"`
	PlateNumber           *string          `json:"plate_number,omitempty"`
	MaxSpeed              *float64         `json:"max_speed,omitempty"`
	MaxSpeedDuration      *int             `json:"max_speed_duration,omitempty"`
	Amount                *float64         `json:"amount,omitempty"`
	BillingCycle          *uint            `json:"billing_cycle,omitempty"`
	IsLifetime            *bool            `json:"is_lifetime,omitempty" gorm:"default:false"`
	NextBillingDate       *time.Time       `json:"next_billing_date,omitempty" gorm:"type:date"`
	SubscriptionStartDate *time.Time       `json:"subscription_start_date,omitempty"`
	SubscriptionEndDate   *time.Time       `json:"subscription_end_date,omitempty"`
	Icon                  *string          `json:"icon,omitempty"`
	Model                 *string          `json:"model,omitempty"`
	Make                  *string          `json:"make,omitempty"`
	ManufactureYear       *string          `json:"manufacture_year,omitempty"`
	Color                 *string          `json:"color,omitempty"`
	EngineNumber          *string          `json:"engine_number,omitempty"`
	ChassisNumber         *string          `json:"chassis_number,omitempty"`
	Vin                   *string          `json:"vin,omitempty"`
	ObdProtocol           *string          `json:"obd_protocol,omitempty"`
	FuelType              *string          `json:"fuel_type,omitempty"`
	FuelRate              *float64         `json:"fuel_rate,omitempty"`
	FuelRateUnit          *string          `json:"fuel_rate_unit,omitempty"`
	Manufacturer          *string          `json:"manufacturer,omitempty"`
	Status                string           `json:"status,omitempty"`
	InsuranceExpiryDate   *time.Time       `json:"insurance_expiry_date,omitempty"`
	InitialMileage        *float64         `json:"initial_mileage,omitempty"`
	Mileage               *float64         `json:"mileage,omitempty"`
	CurrentMileage        *float64         `json:"current_mileage,omitempty"`
	ServiceMileage        *float64         `json:"service_mileage,omitempty"`
	DistanceCovered       *float64         `json:"distance_covered,omitempty"`
	ServiceDays           *uint            `json:"service_days,omitempty"`
	LastServiceDate       *time.Time       `json:"last_service_date,omitempty"`
	NextServiceDate       *time.Time       `json:"next_service_date,omitempty"`
	LastServiceMileage    *float64         `json:"last_service_mileage,omitempty"`
	NextServiceMileage    *float64         `json:"next_service_mileage,omitempty"`
	Emails                *json.RawMessage `json:"emails,omitempty"`
	PhoneNumbers          *json.RawMessage `json:"phone_numbers,omitempty"`
	Description           *string          `json:"description,omitempty"`
	AlarmEvents           bool             `json:"alarm_events,omitempty"`
	TowingEvents          bool             `json:"towing_events,omitempty"`
	TripStartEvents       bool             `json:"trip_start_events,omitempty"`
	TripEndEvents         bool             `json:"trip_end_events,omitempty"`
	GeofenceEntryEvents   bool             `json:"geofence_entry_events,omitempty"`
	GeofenceExitEvents    bool             `json:"geofence_exit_events,omitempty"`
	BatteryLevel          *float64         `json:"battery_level,omitempty"`
	SignalStrength        *float64         `json:"signal_strength,omitempty"`
	LastStatusUpdate      *time.Time       `json:"last_status_update,omitempty"`
}

type DeviceStatus string

const (
	StatusMoving  DeviceStatus = "moving"
	StatusIdling  DeviceStatus = "idling"
	StatusStopped DeviceStatus = "stopped"
	StatusOffline DeviceStatus = "offline"
)
