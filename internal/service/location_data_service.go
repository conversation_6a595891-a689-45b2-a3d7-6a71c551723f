package service

import (
	"encoding/json"
	"fmt"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

type GPSData struct {
	Timestamp      time.Time
	DeviceId       string
	Latitude       float64
	Longitude      float64
	Altitude       float64
	Speed          float64
	Temperature    float64
	BatteryLevel   float64
	Direction      string
	VehicleStatus  string
	IgnitionStatus bool
	CellID         string
	Mnc            string
	Mcc            string
	Lac            string
	Cid            string
	RawData        string
	AdditionalData json.RawMessage
}

func (gpsData *GPSData) SaveGPSData() bool {
	//check for client device id
	var clientDevice models.ClientDevice
	config.DB.Where("device_id", gpsData.DeviceId).First(&clientDevice)
	if clientDevice.Id != 0 {
		gps := &models.GPSData{
			ClientDeviceId: &clientDevice.Id,
			GPSTimestamp:   &gpsData.Timestamp,
			DeviceId:       gpsData.DeviceId,
			Latitude:       gpsData.Latitude,
			Longitude:      gpsData.Longitude,
			Altitude:       &gpsData.Altitude,
			Speed:          &gpsData.Speed,
			Temperature:    &gpsData.Temperature,
			BatteryLevel:   &gpsData.BatteryLevel,
			Direction:      &gpsData.Direction,
			VehicleStatus:  &gpsData.VehicleStatus,
			IgnitionStatus: &gpsData.IgnitionStatus,
			CellId:         &gpsData.CellID,
			Mcc:            &gpsData.Mcc,
			Mnc:            &gpsData.Mnc,
			Lac:            &gpsData.Lac,
			RawData:        &gpsData.RawData,
			AdditionalData: &gpsData.AdditionalData,
		}
		result := config.DB.Create(gps)
		if result.Error != nil {
			return false
		}
		//UpdateDeviceLastLocation(&clientDevice, gpsData.Latitude, gpsData.Longitude, gpsData.Speed)
	} else {
		fmt.Println("Could not find device")
	}
	return false
}
func UpdateDeviceLastLocation(device *models.ClientDevice, latitude float64, longitude float64, speed float64) {
	if device.Latitude != nil && device.Longitude != nil {
		distance := utils.HaversineDistance(*device.Latitude, *device.Longitude, latitude, longitude)
		if distance > 0.01 && speed > 0 {
			zero := 0.0
			if device.DistanceCovered == nil {

				device.DistanceCovered = &zero
				*device.DistanceCovered += distance
			}
			if device.Mileage == nil {
				zero = 0.0
				device.Mileage = &zero
				*device.Mileage += distance
			}

		}
	}
	device.Latitude = &latitude
	device.Longitude = &longitude
	// Update status (moving, idling, stopped)
	var status string
	if speed > 0 {
		status = "moving"
	} else if speed == 0 && device.LastStatusUpdate != nil && time.Since(*device.LastStatusUpdate) < 10*time.Minute {
		status = "idling"
	} else {
		status = "stopped"
	}
	device.DeviceStatus = &status
	timeNow := time.Now()
	device.LastStatusUpdate = &timeNow

	config.DB.Updates(device)
}
