package mail

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

// SendEmail sends a simple email using the mail service
func SendEmail(to []string, subject, message string, isHTML bool) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping email to %v", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendSimpleMail(to, subject, message, isHTML)
}

// SendEmailWithCC sends an email with CC recipients
func SendEmailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithCC(to, cc, subject, message, isHTML)
}

// SendEmailWithAttachment sends an email with an attachment
func SendEmailWithAttachment(to []string, subject, message string, isHTML bool, filename string, content []byte, contentType string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithAttachment(to, subject, message, isHTML, filename, content, contentType)
}

// SendWelcomeEmail sends a welcome email to new users
func SendWelcomeEmail(to string, name string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendWelcomeEmail(to, name)
}

// SendPasswordResetEmail sends a password reset email
func SendPasswordResetEmail(to string, name string, resetLink string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping password reset email to %s", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendPasswordResetEmail(to, name, resetLink)
}

// SendNewAccountEmail sends a new account creation notification with credentials
func SendNewAccountEmail(to string, name string, password string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping new account email to %s", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	loginURL := mailService.NewEmailTemplate().AppURL + "/login"
	return mailService.SendNewAccountEmail(to, name, password, loginURL)
}

// SendNotificationEmail sends a general notification email using template
func SendNotificationEmail(userEmail, userName, message string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendTemplatedEmail(userEmail, "YoTracker Notification", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", userName)).
			AddContent(message)
	})
}

// SendDeviceAlert sends a device alert email using template
func SendDeviceAlert(userEmail, deviceId, alertType, alertMessage string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	subject := fmt.Sprintf("Device Alert: %s - %s", deviceId, alertType)

	return mailService.SendTemplatedEmail(userEmail, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting("Device Alert").
			AddContent(
				fmt.Sprintf("Device ID: %s", deviceId),
				fmt.Sprintf("Alert Type: %s", alertType),
				fmt.Sprintf("Message: %s", alertMessage),
				"Please check your device status in the YoTracker dashboard.",
			).
			SetAction("View Dashboard", template.AppURL+"/dashboard")
	})
}

// SendInvoiceEmail sends an invoice email with attachment using template
func SendInvoiceEmail(invoice *models.Invoice, pdfContent []byte) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}
	invoiceId := strconv.Itoa(int(invoice.Id))
	invoiceReference := *invoice.Reference
	userName := invoice.Client.Name
	userEmail := invoice.Client.Email
	subject := fmt.Sprintf("Invoice %s - YoTracker", invoiceReference)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", userName)).
		AddContent(
			fmt.Sprintf("A new invoice has been generated for you: Invoice %s", invoiceReference),
			"Please find your invoice attached.",
			"Thank you for your business!",
		).
		SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{userEmail}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	// Add PDF attachment
	filename := fmt.Sprintf("invoice_%s.pdf", *invoice.Reference)
	mailObj.AddAttachment(filename, pdfContent, "application/pdf")

	return mailService.SendMail(mailObj)
}

// SendBulkEmail sends the same email to multiple recipients
func SendBulkEmail(recipients []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send to all recipients at once (they'll all see each other's emails)
	// If you want to send individual emails, use SendBulkEmailIndividual instead
	return mailService.SendSimpleMail(recipients, subject, message, isHTML)
}

// SendBulkEmailIndividual sends individual emails to multiple recipients
func SendBulkEmailIndividual(recipients []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send individual emails to each recipient
	for _, recipient := range recipients {
		err := mailService.SendSimpleMail([]string{recipient}, subject, message, isHTML)
		if err != nil {
			return fmt.Errorf("failed to send email to %s: %v", recipient, err)
		}
	}

	return nil
}

// SendInvoicePaymentEmail sends a payment confirmation email using template
func SendInvoicePaymentEmail(payment *models.InvoicePayment) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Extract necessary information
	invoiceId := strconv.Itoa(int(payment.Invoice.Id))
	invoiceReference := *payment.Invoice.Reference
	userName := payment.Invoice.Client.Name
	userEmail := payment.Invoice.Client.Email
	paymentAmount := payment.Amount
	paymentType := payment.PaymentType.Name
	currency := payment.Currency.Symbol

	// Format payment date
	paymentDate := "N/A"
	if payment.Date != nil {
		paymentDate = payment.Date.Format("January 2, 2006")
	}

	// Format transaction ID
	transactionId := "N/A"
	if payment.TransId != nil {
		transactionId = *payment.TransId
	}

	subject := fmt.Sprintf("Payment Confirmation - Invoice %s", invoiceReference)

	return mailService.SendTemplatedEmail(userEmail, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", userName)).
			AddContent(
				fmt.Sprintf("We have successfully received your payment for Invoice %s.", invoiceReference),
				"Payment Details:",
				fmt.Sprintf("• Amount: %s%.2f", currency, paymentAmount),
				fmt.Sprintf("• Payment Method: %s", paymentType),
				fmt.Sprintf("• Payment Date: %s", paymentDate),
				fmt.Sprintf("• Transaction ID: %s", transactionId),
				"",
				"Thank you for your prompt payment. Your account has been updated accordingly.",
			).
			SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)
	})
}
