services:
  tcp-server:
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    ports:
      - "${GT06_PORT:-5022}:5022"
      - "${H02_PORT:-5010}:5010"
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
    volumes:
        - .:/app
    depends_on:
      - mysql
    networks:
      - yotracker

  tcp-server-dev:
    build:
      context: .
      dockerfile: docker/server/Dockerfile.dev
    ports:
      - "${GT06_PORT:-5022}:5022"
      - "${H02_PORT:-5010}:5010"
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
    volumes:
      - .:/app
    depends_on:
      - mysql
    networks:
      - yotracker

  gt06-simulator:
    build:
      context: .
      dockerfile: docker/simulator/gt06/Dockerfile
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
    volumes:
      - .:/app
    depends_on:
      - mysql
    networks:
      - yotracker

  h02-simulator:
    build:
      context: .
      dockerfile: docker/simulator/h02/Dockerfile
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
    volumes:
      - .:/app
    depends_on:
      - mysql
    networks:
      - yotracker

  web-backend:
    build:
      context: .
      dockerfile: docker/web/backend/Dockerfile
    ports:
      - "${BACKEND_PORT:-9000}:${BACKEND_PORT:-9000}"
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
      - BACKEND_PORT=${BACKEND_PORT:-9000}
    volumes:
      - .:/app
    depends_on:
      - mysql
    networks:
      - yotracker

  web-backend-dev:
    build:
      context: .
      dockerfile: docker/web/backend/Dockerfile.dev
    ports:
      - "${BACKEND_PORT:-9000}:${BACKEND_PORT:-9000}"
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
      - BACKEND_PORT=${BACKEND_PORT:-9000}
    volumes:
      - .:/app/yotracker
      - ./docker/web/backend/.air.toml:/app/yotracker/.air.toml
      - .env:/app/yotracker/.env
    depends_on:
      - mysql
    networks:
      - yotracker
  web-frontend-dev:
    build:
      context: .
      dockerfile: docker/web/frontend/Dockerfile.dev
    ports:
      - "${FRONTEND_PORT:-9001}:${FRONTEND_PORT:-9001}"
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
      - FRONTEND_PORT=${FRONTEND_PORT:-9001}
    volumes:
      - .:/app/yotracker
      - ./docker/web/frontend/.air.toml:/app/yotracker/.air.toml
      - .env:/app/yotracker/.env
    depends_on:
      - mysql
    networks:
      - yotracker
  cron-dev:
    build:
      context: .
      dockerfile: docker/cron/Dockerfile.dev
    environment:
      - DB_HOST=mysql
      - DB_PORT=${DB_PORT:-3309}
      - DB_USERNAME=${DB_USERNAME:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_DATABASE:-yotracker}
    volumes:
      - .:/app/yotracker
      - ./docker/cron/.air.toml:/app/yotracker/.air.toml
      - .env:/app/yotracker/.env
    depends_on:
      - mysql
    networks:
      - yotracker

  mysql:
    image: mysql:8.0
    ports:
        - '${DB_PORT:-3309}:3306'
    environment:
        MYSQL_ROOT_HOST: '%'
        MYSQL_DATABASE: '${DB_DATABASE:-yotracker}'
        MYSQL_USER: '${DB_USERNAME:-admin}'
        MYSQL_PASSWORD: '${DB_PASSWORD:-password}'
        MYSQL_ROOT_PASSWORD: '${DB_PASSWORD:-password}'
        MYSQL_ALLOW_EMPTY_PASSWORD: 1
    volumes:
        - 'yotracker-mysql:/var/lib/mysql'
        - './docker/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
    networks:
        - yotracker

networks:
  yotracker:
    driver: bridge

volumes:
  yotracker-mysql: