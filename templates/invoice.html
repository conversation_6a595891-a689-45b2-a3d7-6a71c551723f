<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice {{.Reference}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            color: #333;
        }

        .header {
            background-color: #edf1ef;
            padding: 20px;
            display: flex;
            justify-content: space-between;
        }

        .header .company, .header .logo {
            width: 45%;
        }

        .logo {
            text-align: right;
        }

        .section {
            margin-top: 20px;
        }

        .flex {
            display: flex;
            justify-content: space-between;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            border: 1px solid #ccc;
            padding: 8px;
        }

        .table th {
            background-color: #004a77;
            color: white;
        }

        .totals {
            width: 300px;
            float: right;
            margin-top: 20px;
        }

        .totals td {
            padding: 5px;
        }

        .thankyou {
            margin-top: 40px;
            font-size: 18px;
        }

        .payment-info {
            clear: both;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .bold {
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="header">
    <div class="company">
        <h2>{{.BillFrom.Name}}</h2>
        <p>{{.BillFrom.Address}}</p>
        <p>{{.BillFrom.Email}}</p>
    </div>
    <div class="logo">
        {{if .Logo}}
        <img src="{{.Logo}}" alt="Logo" height="80"/>
        {{end}}
    </div>
</div>

<div class="section flex">
    <div>
        <h3>BILL TO</h3>
        <h2>{{.BillTo.Name}}</h2>
        <p>{{.BillTo.Address}}</p>
        <p>{{.BillTo.City}} {{if .BillTo.State}}, {{.BillTo.State}} {{end}} {{if .BillTo.Country}}, {{.BillTo.Country}}
            {{end}}</p>
        <p>{{.BillTo.Email}}</p>
    </div>
    <div>
        <h3>INVOICE DETAILS</h3>
        <p><span class="bold">Invoice No:</span> {{.Reference}}</p>
        <p><span class="bold">Invoice Date:</span> {{.Date}}</p>
        {{if .DueDate}}<p><span class="bold">Due Date:</span> {{.DueDate}}</p>{{end}}
    </div>
</div>

<table class="table">
    <thead>
    <tr>
        <th>Description</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Amount</th>
    </tr>
    </thead>
    <tbody>
    {{range .InvoiceItems}}
    <tr>
        <td>{{.Description}}</td>
        <td>{{.Qty}}</td>
        <td>${{printf "%.2f" .UnitPrice}}</td>
        <td>${{printf "%.2f" .Total}}</td>
    </tr>
    {{end}}
    </tbody>
</table>

<table class="totals">
    <tr>
        <td>Sub Total</td>
        <td class="bold right">${{printf "%.2f" .Subtotal}}</td>
    </tr>
    <tr>
        <td>Tax</td>
        <td class="bold right">${{printf "%.2f" .TaxAmount}}</td>
    </tr>
    <tr>
        <td>Total Due</td>
        <td class="bold right">${{printf "%.2f" .Total}}</td>
    </tr>
</table>

<div class="thankyou">

</div>
<div style="clear: both;margin-top: 140px;">
    <span class="bold">Payment Information</span>
</div>
<div class="payment-info">
    <div>
        <h4>Nostro-USD:</h4>
        <p><span class="bold">Bank:</span> NBS</p>
        <p><span class="bold">Account Name:</span> Webstudio Pvt Ltd</p>
        <p><span class="bold">Account Number:</span> 3255-**********</p>
        <p><span class="bold">Branch:</span> Bindura</p>
    </div>
    <div>
        <h4>ZIG:</h4>
        <p><span class="bold">Bank:</span> NBS</p>
        <p><span class="bold">Account Name:</span> Webstudio Pvt Ltd</p>
        <p><span class="bold">Account Number:</span> 3255-**********</p>
        <p><span class="bold">Branch:</span> Bindura</p>
    </div>
</div>
<div style="clear: both; margin-top: 40px;">
    <span class="bold">Terms & Conditions</span>
</div>
<div>
    <p>{{.Terms}}</p>
</div>
</body>
</html>