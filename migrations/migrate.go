package migrations

import (
	"yotracker/config"
	"yotracker/internal/models"
)

func Migrate() {
	// Migrate the schema
	config.DB.AutoMigrate(
		&models.Alert{},
		&models.Client{},
		&models.ClientDevice{},
		&models.CommandLog{},
		&models.DeviceType{},
		&models.Fleet{},
		&models.GPSData{},
		&models.Protocol{},
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.RolePermission{},
		&models.ClientRole{},
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.PaymentType{},
		&models.Currency{},
		&models.Country{},
		&models.Setting{},
	)

}
