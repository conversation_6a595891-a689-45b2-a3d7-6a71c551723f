package main

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"yotracker/config"
	"yotracker/internal/protocol/gt06"
	"yotracker/internal/protocol/h02"
	"yotracker/migrations"
)

func main() {
	config.InitDB()
	//run migrations
	migrations.Migrate()
	go startServer(5022, "GT06")
	go startServer(5010, "H02")
	// Start pprof (on HTTP port 6060)
	go func() {
		log.Println("pprof running at :6060")
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	// Block the main goroutine forever
	select {}
}
func startServer(port int, protocol string) {
	ln, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		fmt.Printf("Error starting %s server: %s\n", protocol, err.Error())
		os.Exit(1)
	}
	defer func(ln net.Listener) {
		err := ln.Close()
		if err != nil {

		}
	}(ln)
	fmt.Printf("%s server started on port %d\n", protocol, port)
	for {
		conn, err := ln.Accept()
		if err != nil {
			fmt.Printf("Error accepting connection: %s\n", err.Error())
			continue
		}
		if protocol == "GT06" {
			p := gt06.Parser{
				Reader: bufio.NewReader(conn),
				Conn:   conn,
			}
			go p.HandleConnection(conn)
		}
		if protocol == "H02" {
			p := h02.Parser{
				Conn: conn,
			}
			go p.HandleConnection()
		}

	}
}
