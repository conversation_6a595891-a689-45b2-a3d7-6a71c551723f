package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllInvoices(c *gin.Context) {
	var invoices []models.Invoice
	var total int64
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("Currency").Where(filter).Order("id desc").Find(&invoices)
	config.DB.Model(&models.Invoice{}).Where(filter).Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         invoices,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetInvoiceById(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": invoice,
	})
}

func CreateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)

	invoice := models.Invoice{
		ClientId:             req.ClientId,
		CreatedById:          &userID,
		CouponId:             req.CouponId,
		TaxRateId:            req.TaxRateId,
		PaymentTypeId:        req.PaymentTypeId,
		CurrencyId:           req.CurrencyId,
		Date:                 req.Date,
		DueDate:              req.DueDate,
		Amount:               req.Amount,
		Discount:             req.Discount,
		DiscountType:         req.DiscountType,
		Status:               req.Status,
		Balance:              req.Amount,
		Xrate:                req.Xrate,
		DiscountAmount:       req.DiscountAmount,
		CouponDiscountAmount: req.CouponDiscountAmount,
		TaxAmount:            req.TaxAmount,
		AdminNotes:           req.AdminNotes,
		Terms:                req.Terms,
		Recurring:            req.Recurring,
		RecurFrequency:       req.RecurFrequency,
		RecurStartDate:       req.RecurStartDate,
		RecurEndDate:         req.RecurEndDate,
		RecurNextDate:        req.RecurNextDate,
		Description:          req.Description,
		IsSubscription:       req.IsSubscription,
	}
	//if is_subscription, set recurring to true and get billing cycle from client
	if req.IsSubscription != nil && *req.IsSubscription {
		invoice.Recurring = req.IsSubscription
		var client models.Client
		if err := config.DB.First(&client, req.ClientId).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"message": "Client not found",
			})
			return
		}
		if client.BillingCycle != nil {
			recurFrequency := 30
			billingCycle := *client.BillingCycle
			billingDay := client.BillingDay
			if client.BillingCycle == "monthly" {
				recurFrequency = 30
			} else if client.BillingCycle == "quarterly" {
				recurFrequency = 90
			} else if client.BillingCycle == "half_yearly" {
				recurFrequency = 180
			} else if client.BillingCycle == "yearly" {
				recurFrequency = 365
			}
			invoice.RecurFrequency = client.BillingCycle
		}
	}

	// Use a transaction to ensure both invoice and items are created together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Create invoice items if provided
	if len(req.InvoiceItems) > 0 {
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:            invoice.Id,
				ClientDeviceId:       req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:            req.InvoiceItems[i].TaxRateId,
				Name:                 req.InvoiceItems[i].Name,
				Description:          req.InvoiceItems[i].Description,
				Quantity:             req.InvoiceItems[i].Quantity,
				ItemPosition:         req.InvoiceItems[i].ItemPosition,
				UnitCost:             req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost: req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:             req.InvoiceItems[i].Discount,
				DiscountType:         req.InvoiceItems[i].DiscountType,
				DiscountAmount:       req.InvoiceItems[i].DiscountAmount,
				TaxAmount:            req.InvoiceItems[i].TaxAmount,
				Total:                req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}
	//generate reference
	reference := utils.GenerateReference(strconv.Itoa(int(invoice.Id)))
	config.DB.Model(&invoice).Update("reference", reference)

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice created successfully",
		"data":    completeInvoice,
	})
}

func UpdateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	if invoice.Reference == nil {
		reference := utils.GenerateReference(c.Param("id"))
		invoice.Reference = &reference
	}
	invoice.ClientId = req.ClientId
	invoice.CouponId = req.CouponId
	invoice.TaxRateId = req.TaxRateId
	invoice.PaymentTypeId = req.PaymentTypeId
	invoice.CurrencyId = req.CurrencyId

	invoice.Date = req.Date
	invoice.DueDate = req.DueDate
	invoice.Amount = req.Amount
	invoice.Discount = req.Discount
	invoice.DiscountType = req.DiscountType
	invoice.Status = req.Status
	invoice.Xrate = req.Xrate
	invoice.DiscountAmount = req.DiscountAmount
	invoice.CouponDiscountAmount = req.CouponDiscountAmount
	invoice.TaxAmount = req.TaxAmount
	invoice.AdminNotes = req.AdminNotes
	invoice.Terms = req.Terms
	invoice.Recurring = req.Recurring
	invoice.RecurFrequency = req.RecurFrequency
	invoice.RecurStartDate = req.RecurStartDate
	invoice.RecurEndDate = req.RecurEndDate
	invoice.RecurNextDate = req.RecurNextDate
	invoice.Description = req.Description
	invoice.IsSubscription = req.IsSubscription

	// Use a transaction to ensure both invoice and items are updated together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Save(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Handle invoice items if provided
	if len(req.InvoiceItems) > 0 {
		// Delete existing invoice items
		if err := tx.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{}).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to delete existing invoice items: " + err.Error(),
			})
			return
		}

		// Create new invoice items
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:            invoice.Id,
				ClientDeviceId:       req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:            req.InvoiceItems[i].TaxRateId,
				Name:                 req.InvoiceItems[i].Name,
				Description:          req.InvoiceItems[i].Description,
				Quantity:             req.InvoiceItems[i].Quantity,
				ItemPosition:         req.InvoiceItems[i].ItemPosition,
				UnitCost:             req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost: req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:             req.InvoiceItems[i].Discount,
				DiscountType:         req.InvoiceItems[i].DiscountType,
				DiscountAmount:       req.InvoiceItems[i].DiscountAmount,
				TaxAmount:            req.InvoiceItems[i].TaxAmount,
				Total:                req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}
	UpdateInvoiceStatus(invoice.Id)

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice updated successfully",
		"data":    completeInvoice,
	})
}

func DeleteInvoice(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	result := config.DB.Delete(&invoice)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	//delete invoice items
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{})
	//delete invoice payments
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoicePayment{})
	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice deleted successfully",
	})
}

func SearchInvoices(c *gin.Context) {
	var invoices []models.Invoice
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}
	if isSubscription := c.Query("is_subscription"); isSubscription != "" {
		filter["is_subscription"] = isSubscription
	}

	if reference := c.Query("reference"); reference != "" {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Where("reference LIKE ?", "%"+reference+"%").Order("id desc").Find(&invoices)
	} else {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Order("id desc").Find(&invoices)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": invoices,
	})
}
func UpdateInvoiceStatus(id uint) error {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, id).Error; err != nil {
		return err
	}
	var totalPayments float64
	config.DB.Model(&models.InvoicePayment{}).Where("invoice_id = ?", invoice.Id).Select("COALESCE(SUM(amount), 0)").Scan(&totalPayments)
	balance := *invoice.Amount - totalPayments
	invoice.Balance = &balance
	if balance == 0 {
		invoice.Status = "paid"
	} else if balance < 0 {
		invoice.Status = "overpaid"
	} else if balance > 0 {
		invoice.Status = "partial"
	}
	result := config.DB.Save(&invoice)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// calculateRecurringDates calculates the start date and next billing date based on billing cycle and billing day
func calculateRecurringDates(billingCycle string, billingDay uint) (time.Time, time.Time) {
	now := time.Now()

	// For start date, we use today's date
	startDate := now

	var nextDate time.Time

	switch billingCycle {
	case "monthly":
		nextDate = calculateNextMonthlyDate(now, int(billingDay))
	case "quarterly":
		nextDate = calculateNextQuarterlyDate(now, int(billingDay))
	case "half_yearly":
		nextDate = calculateNextHalfYearlyDate(now, int(billingDay))
	case "yearly":
		nextDate = calculateNextYearlyDate(now, int(billingDay))
	default:
		// Default to monthly if unknown cycle
		nextDate = calculateNextMonthlyDate(now, int(billingDay))
	}

	return startDate, nextDate
}

// calculateNextMonthlyDate calculates the next monthly billing date
func calculateNextMonthlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Create the target date for this month
	targetDate := time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in this month (e.g., Feb 30), use the last day of the month
	if targetDate.Month() != month {
		targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location()) // Last day of current month
	}

	// If the target date has already passed this month, move to next month
	if targetDate.Before(from) || targetDate.Equal(from) {
		nextMonth := month + 1
		nextYear := year
		if nextMonth > 12 {
			nextMonth = 1
			nextYear++
		}

		targetDate = time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextMonth {
			targetDate = time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextQuarterlyDate calculates the next quarterly billing date (every 3 months)
func calculateNextQuarterlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next quarter month
	var nextQuarterMonth time.Month
	switch {
	case month <= 3:
		nextQuarterMonth = 3
	case month <= 6:
		nextQuarterMonth = 6
	case month <= 9:
		nextQuarterMonth = 9
	case month <= 12:
		nextQuarterMonth = 12
	}

	targetDate := time.Date(year, nextQuarterMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextQuarterMonth {
		targetDate = time.Date(year, nextQuarterMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed, move to next quarter
	if targetDate.Before(from) || targetDate.Equal(from) {
		nextQuarterMonth += 3
		if nextQuarterMonth > 12 {
			nextQuarterMonth -= 12
			year++
		}

		targetDate = time.Date(year, nextQuarterMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextQuarterMonth {
			targetDate = time.Date(year, nextQuarterMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextHalfYearlyDate calculates the next half-yearly billing date (every 6 months)
func calculateNextHalfYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next half-year month (June or December)
	var nextHalfYearMonth time.Month
	if month <= 6 {
		nextHalfYearMonth = 6
	} else {
		nextHalfYearMonth = 12
	}

	targetDate := time.Date(year, nextHalfYearMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextHalfYearMonth {
		targetDate = time.Date(year, nextHalfYearMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed, move to next half-year
	if targetDate.Before(from) || targetDate.Equal(from) {
		if nextHalfYearMonth == 6 {
			nextHalfYearMonth = 12
		} else {
			nextHalfYearMonth = 6
			year++
		}

		targetDate = time.Date(year, nextHalfYearMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextHalfYearMonth {
			targetDate = time.Date(year, nextHalfYearMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextYearlyDate calculates the next yearly billing date
func calculateNextYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, day := from.Date()

	// Try to create the target date for this year
	targetDate := time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in this month, use the last day of the month
	if targetDate.Month() != month {
		targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed this year, move to next year
	if targetDate.Before(from) || (targetDate.Equal(from) && day >= billingDay) {
		year++
		targetDate = time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != month {
			targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}
